<template>
  <div class="textarea-field-demo">
    <h1>TextareaField 组件演示</h1>
    <p>这是一个带有浮动标签和表单验证功能的 Textarea 组件</p>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>基础文本域</h3>
          <sp-textarea-field
            v-model:value="basicValue"
            label="基础文本域"
            name="basic"
            placeholder="请输入内容..."
            :rows="3"
          />
          <p class="value-display">值: {{ basicValue || '(空)' }}</p>
        </div>

        <div class="demo-item">
          <h3>必填字段</h3>
          <sp-textarea-field
            v-model:value="requiredValue"
            label="必填文本域"
            name="required"
            placeholder="这是必填字段..."
            :required="true"
            :rows="4"
          />
          <p class="value-display">值: {{ requiredValue || '(空)' }}</p>
        </div>
      </div>
    </section>

    <!-- 功能特性 -->
    <section class="demo-section">
      <h2>功能特性</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>带字数统计</h3>
          <sp-textarea-field
            v-model:value="countValue"
            label="限制字数"
            name="count"
            placeholder="最多100字..."
            :maxlength="100"
            :show-word-limit="true"
            :rows="3"
          />
        </div>

        <div class="demo-item">
          <h3>可清除</h3>
          <sp-textarea-field
            v-model:value="clearableValue"
            label="可清除文本域"
            name="clearable"
            placeholder="可以清除内容..."
            :clearable="true"
            :rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 自动调整高度 -->
    <section class="demo-section">
      <h2>自动调整高度</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>自动高度</h3>
          <sp-textarea-field
            v-model:value="autosizeValue"
            label="自动高度"
            name="autosize"
            placeholder="高度会自动调整..."
            :autosize="{ minRows: 2, maxRows: 6 }"
          />
        </div>

        <div class="demo-item">
          <h3>固定行数</h3>
          <sp-textarea-field
            v-model:value="fixedValue"
            label="固定5行"
            name="fixed"
            placeholder="固定5行高度..."
            :rows="5"
          />
        </div>
      </div>
    </section>

    <!-- 状态演示 -->
    <section class="demo-section">
      <h2>状态演示</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>禁用状态</h3>
          <sp-textarea-field
            v-model:value="disabledValue"
            label="禁用文本域"
            name="disabled"
            placeholder="这是禁用状态..."
            :disabled="true"
            :rows="3"
          />
        </div>

        <div class="demo-item">
          <h3>只读状态</h3>
          <sp-textarea-field
            v-model:value="readonlyValue"
            label="只读文本域"
            name="readonly"
            :readonly="true"
            :rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 帮助文本和验证 -->
    <section class="demo-section">
      <h2>帮助文本和验证</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>帮助文本</h3>
          <sp-textarea-field
            v-model:value="helperValue"
            label="带帮助文本"
            name="helper"
            placeholder="输入一些内容..."
            helper-text="这是帮助文本，用于说明字段用途"
            :rows="3"
          />
        </div>

        <div class="demo-item">
          <h3>错误状态</h3>
          <sp-textarea-field
            v-model:value="errorValue"
            label="错误状态"
            name="error"
            placeholder="这是错误状态..."
            :error="true"
            :rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 变体样式 -->
    <section class="demo-section">
      <h2>🎨 变体样式</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>默认样式</h3>
          <sp-textarea-field
            v-model:value="defaultVariantValue"
            label="默认边框"
            name="default-variant"
            variant="default"
            placeholder="默认边框样式"
            :rows="3"
          />
        </div>

        <div class="demo-item">
          <h3>下划线样式</h3>
          <sp-textarea-field
            v-model:value="underlinedVariantValue"
            label="下划线文本域"
            name="underlined-variant"
            variant="underlined"
            placeholder="简洁的下划线样式"
            :rows="3"
          />
          <p class="resize-tip">点击查看标签上浮和下划线扩展动画</p>
        </div>

        <div class="demo-item">
          <h3>填充样式</h3>
          <sp-textarea-field
            v-model:value="filledVariantValue"
            label="填充文本域"
            name="filled-variant"
            variant="filled"
            placeholder="带背景的填充样式"
            :rows="3"
          />
          <p class="resize-tip">背景色填充，标签浮动到内部顶部</p>
        </div>

        <div class="demo-item">
          <h3>方形样式</h3>
          <sp-textarea-field
            v-model:value="squareVariantValue"
            label="方形文本域"
            name="square-variant"
            variant="square"
            placeholder="方正的边框样式"
            :rows="3"
          />
          <p class="resize-tip">无圆角，方正设计</p>
        </div>

        <div class="demo-item">
          <h3>无边框样式</h3>
          <sp-textarea-field
            v-model:value="unborderVariantValue"
            label="无边框文本域"
            name="unborder-variant"
            variant="unborder"
            placeholder="极简无边框设计"
            :rows="3"
          />
          <p class="resize-tip">完全无边框，hover 显示背景</p>
        </div>
      </div>
    </section>

    <!-- 发光效果 -->
    <section class="demo-section">
      <h2>✨ 发光效果</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>默认发光</h3>
          <sp-textarea-field
            v-model:value="glowDefaultValue"
            label="发光文本域"
            name="glow-default"
            variant="default"
            effect="glow"
            placeholder="聚焦时有发光效果"
            :rows="3"
          />
        </div>

        <div class="demo-item">
          <h3>下划线发光</h3>
          <sp-textarea-field
            v-model:value="glowUnderlineValue"
            label="发光下划线"
            name="glow-underline"
            variant="underlined"
            effect="glow"
            placeholder="下划线 + 发光效果"
            :rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>📏 尺寸变体</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>小尺寸</h3>
          <sp-textarea-field
            v-model:value="smallValue"
            label="小尺寸"
            name="small"
            placeholder="小尺寸文本域..."
            size="small"
            :rows="3"
          />
        </div>

        <div class="demo-item">
          <h3>中等尺寸（默认）</h3>
          <sp-textarea-field
            v-model:value="mediumValue"
            label="中等尺寸"
            name="medium"
            placeholder="中等尺寸文本域..."
            size="medium"
            :rows="3"
          />
        </div>

        <div class="demo-item">
          <h3>大尺寸</h3>
          <sp-textarea-field
            v-model:value="largeValue"
            label="大尺寸"
            name="large"
            placeholder="大尺寸文本域..."
            size="large"
            :rows="3"
          />
        </div>
      </div>
    </section>

    <!-- 调整大小选项 -->
    <section class="demo-section">
      <h2>调整大小选项</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>垂直调整（默认）</h3>
          <sp-textarea-field
            v-model:value="resizeVerticalValue"
            label="垂直调整"
            name="resize-vertical"
            placeholder="可以垂直调整大小..."
            resize="vertical"
            :rows="3"
          />
          <p class="resize-tip">👆 应该能看到右下角的拖拽图标</p>
        </div>

        <div class="demo-item">
          <h3>禁止调整</h3>
          <sp-textarea-field
            v-model:value="resizeNoneValue"
            label="禁止调整"
            name="resize-none"
            placeholder="不能调整大小..."
            resize="none"
            :rows="3"
          />
          <p class="resize-tip">👆 没有拖拽图标（正常）</p>
        </div>

        <div class="demo-item">
          <h3>双向调整</h3>
          <sp-textarea-field
            v-model:value="resizeBothValue"
            label="双向调整"
            name="resize-both"
            placeholder="可以双向调整大小..."
            resize="both"
            :rows="3"
          />
          <p class="resize-tip">👆 应该能看到右下角的拖拽图标</p>
        </div>
      </div>
    </section>

    <!-- 拖拽图标测试 -->
    <section class="demo-section">
      <h2>🔧 拖拽图标测试</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <h3>原生 textarea（对比）</h3>
          <textarea
            v-model="dragTestValue1"
            placeholder="原生 textarea，应该有拖拽图标..."
            style="
              width: 100%;
              min-height: 80px;
              padding: 16px 12px;
              border: 1px solid #ddd;
              border-radius: 6px;
              resize: vertical;
              font-family: inherit;
              font-size: 16px;
            "
          ></textarea>
          <p class="resize-tip">👆 原生 textarea 的拖拽图标</p>
        </div>

        <div class="demo-item">
          <h3>TextareaField 无后缀</h3>
          <sp-textarea-field
            v-model:value="dragTestValue1"
            label="无后缀"
            name="drag-test-1"
            placeholder="应该能看到拖拽图标..."
            resize="vertical"
            :rows="4"
          />
          <p class="resize-tip">👆 应该能看到拖拽图标</p>
        </div>

        <div class="demo-item">
          <h3>TextareaField 有后缀</h3>
          <sp-textarea-field
            v-model:value="dragTestValue2"
            label="有后缀"
            name="drag-test-2"
            placeholder="有后缀但仍能拖拽..."
            resize="vertical"
            :rows="4"
            :clearable="true"
            :show-word-limit="true"
            :maxlength="200"
          />
          <p class="resize-tip">👆 应该能看到拖拽图标（在后缀内容右下方）</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 响应式数据
  const basicValue = ref('')
  const requiredValue = ref('')
  const countValue = ref('')
  const clearableValue = ref('可以清除的内容')
  const autosizeValue = ref(
    '这是一段较长的文本，用于测试自动调整高度功能。当内容增加时，文本域的高度会自动调整。\n\n你可以继续输入更多内容来看到效果。'
  )
  const fixedValue = ref('')
  const disabledValue = ref('这是禁用状态的内容')
  const readonlyValue = ref('这是只读状态的内容，不能编辑')
  const helperValue = ref('')
  const errorValue = ref('这是错误状态的内容')

  // 变体样式
  const defaultVariantValue = ref('')
  const underlinedVariantValue = ref('')
  const filledVariantValue = ref('')
  const squareVariantValue = ref('')
  const unborderVariantValue = ref('')

  // 发光效果
  const glowDefaultValue = ref('')
  const glowUnderlineValue = ref('')

  // 尺寸变体
  const smallValue = ref('')
  const mediumValue = ref('')
  const largeValue = ref('')

  // 调整大小
  const resizeVerticalValue = ref('')
  const resizeNoneValue = ref('')
  const resizeBothValue = ref('')

  // 拖拽测试
  const dragTestValue1 = ref('')
  const dragTestValue2 = ref('测试内容')
</script>

<style scoped>
  .textarea-field-demo {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
  }

  .demo-section h2 {
    color: #333;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
  }

  .demo-item {
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-item h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #555;
    font-size: 16px;
  }

  .value-display {
    margin-top: 10px;
    padding: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    word-break: break-all;
  }

  .resize-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #888;
    font-style: italic;
  }
</style>
