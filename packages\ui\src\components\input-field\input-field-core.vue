<!--
  InputFieldCore.vue - 基于 FieldContainer 的 InputField 核心实现
  使用通用的 FieldContainer 提供浮动标签、表单验证等功能
-->

<template>
  <FieldContainer
    ref="fieldContainerRef"
    v-bind="containerProps"
    :class="[
      hasPrefix && 'sp-field-container--has-prefix',
      hasSuffix && 'sp-field-container--has-suffix',
    ]"
    @update:value="handleValueUpdate"
    @input="handleInput"
    @change="handleChange"
    @focus="handleFocus"
    @blur="handleBlur"
    @keydown="handleKeydown"
    @wrapper-click="handleWrapperClick"
    @label-click="handleLabelClick"
    @validate="handleValidate"
  >
    <!-- 前缀区域 -->
    <template
      #prefix="slotProps"
      v-if="hasPrefix"
    >
      <div :class="slotProps.prefixClasses">
        <!-- 前缀图标 -->
        <sp-icon
          v-if="prefixIcon"
          :name="prefixIcon"
          :size="slotProps.iconSize"
          :class="prefixIconClasses"
        />
        <!-- 前缀插槽 -->
        <slot name="prefix" />
      </div>
    </template>

    <!-- 输入元素 -->
    <template #default="slotProps">
      <input
        :id="slotProps.fieldId"
        ref="inputRef"
        :type="actualType"
        :value="value"
        :placeholder="slotProps.placeholder"
        :disabled="slotProps.disabled"
        :readonly="slotProps.readonly"
        :maxlength="maxlength"
        :minlength="minlength"
        :autocomplete="autocomplete"
        :autofocus="autofocus"
        :inputmode="inputmode"
        :step="step"
        :min="min"
        :max="max"
        :class="[slotProps.inputClasses, inputSpecificClasses]"
        :style="slotProps.inputStyle"
        @input="slotProps.onInput"
        @change="slotProps.onChange"
        @focus="slotProps.onFocus"
        @blur="slotProps.onBlur"
        @keydown="slotProps.onKeydown"
        @keyup="handleKeyup"
        @keypress="handleKeypress"
        @click="handleClick"
      />
    </template>

    <!-- 后缀区域 -->
    <template
      #suffix="slotProps"
      v-if="hasSuffix"
    >
      <div :class="slotProps.suffixClasses">
        <!-- 清除按钮 -->
        <sp-icon
          v-if="showClearIcon"
          name="CloseCircle"
          :size="slotProps.iconSize"
          clickable
          :class="clearIconClasses"
          @click.stop.prevent="handleClear"
          @mousedown.stop.prevent
        />

        <!-- 密码显示切换 -->
        <sp-icon
          v-if="showPasswordIcon"
          :name="passwordIconName"
          :size="slotProps.iconSize"
          clickable
          :class="passwordIconClasses"
          @click.stop.prevent="togglePassword"
          @mousedown.stop.prevent
        />

        <!-- 字数统计 -->
        <span
          v-if="showWordLimit"
          :class="wordCountClasses"
        >
          {{ wordCount }}/{{ maxlength }}
        </span>

        <!-- 后缀图标 -->
        <sp-icon
          v-if="suffixIcon"
          :name="suffixIcon"
          :size="slotProps.iconSize"
          :class="suffixIconClasses"
        />

        <!-- 后缀插槽 -->
        <slot name="suffix" />
      </div>
    </template>
  </FieldContainer>
</template>

<script setup lang="ts">
  import { computed, ref, useSlots } from 'vue'
  import SpIcon from '../icon/Icon.vue'
  import FieldContainer from '../field-container/FieldContainer.vue'
  import { useInputLogic } from '../../composables'
  import type { InputFieldProps, InputFieldEmits } from './types'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<InputFieldProps>(), {
    value: '',
    type: 'text',
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    clearable: false,
    showPassword: false,
    showWordLimit: false,
    error: false,
    loading: false,
    required: false,
    showMessage: true,
    persistentLabel: false,
  })

  const emit = defineEmits<InputFieldEmits>()
  const slots = useSlots()

  // ===== 组件引用 =====
  const fieldContainerRef = ref<InstanceType<typeof FieldContainer>>()
  const inputRef = ref<HTMLInputElement>()

  // ===== 使用 Input 逻辑 Composable =====
  const {
    // 状态
    passwordVisible,

    // 计算属性
    actualType,
    hasValue,
    wordCount,

    // 显示逻辑
    showClearIcon,
    showPasswordIcon,
    passwordIconName,

    // 方法
    focus: focusInput,
    blur: blurInput,
    select: selectInput,
    clear: clearInput,
    togglePassword,
  } = useInputLogic(props, emit)

  // ===== 传递给 FieldContainer 的属性 =====
  const containerProps = computed(() => ({
    value: props.value,
    label: props.label,
    name: props.name || '',
    placeholder: props.placeholder,
    disabled: props.disabled,
    readonly: props.readonly,
    required: props.required,
    error: props.error,
    loading: props.loading,
    variant: props.variant,
    effect: props.effect === 'shadow' ? 'glow' : props.effect, // 转换不支持的 effect
    size: props.size,
    rules: props.rules,
    showMessage: props.showMessage,
    helperText: props.helperText,
    persistentLabel: props.persistentLabel,
  }))

  // ===== 前缀/后缀显示逻辑 =====
  const hasPrefix = computed(() => {
    return !!(props.prefixIcon || slots.prefix)
  })

  const hasSuffix = computed(() => {
    return !!(
      showClearIcon.value ||
      showPasswordIcon.value ||
      props.showWordLimit ||
      props.suffixIcon ||
      slots.suffix
    )
  })

  // ===== Input 特有样式 =====
  const inputSpecificClasses = computed(() => ['sp-input-field__input'])

  const prefixIconClasses = computed(() => ['sp-input-field__prefix-icon'])
  const suffixIconClasses = computed(() => ['sp-input-field__suffix-icon'])
  const clearIconClasses = computed(() => ['sp-input-field__clear'])
  const passwordIconClasses = computed(() => ['sp-input-field__password'])
  const wordCountClasses = computed(() => ['sp-input-field__count'])

  // ===== 事件处理 =====
  const handleValueUpdate = (value: string | number | undefined) => {
    emit('update:value', value)
  }

  const handleInput = (event: Event) => {
    emit('input', event)
  }

  const handleChange = (event: Event) => {
    emit('change', event)
  }

  const handleFocus = (event: FocusEvent) => {
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    emit('blur', event)
  }

  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
    // 处理回车事件
    if (event.key === 'Enter') {
      emit('enter', event)
    }
  }

  const handleKeyup = (event: KeyboardEvent) => {
    emit('keyup', event)
  }

  const handleKeypress = (event: KeyboardEvent) => {
    emit('keypress', event)
  }

  const handleClick = (event: MouseEvent) => {
    emit('click', event)
  }

  const handleWrapperClick = () => {
    if (!props.disabled && !props.readonly) {
      focus()
    }
  }

  const handleLabelClick = () => {
    if (!props.disabled && !props.readonly) {
      focus()
    }
  }

  const handleClear = () => {
    clearInput()
    emit('clear')
  }

  const handleValidate = (name: string, isValid: boolean, message: string) => {
    emit('validate', name, isValid, message)
  }

  // ===== 暴露的方法 =====
  const focus = () => {
    inputRef.value?.focus()
  }

  const blur = () => {
    inputRef.value?.blur()
  }

  const select = () => {
    inputRef.value?.select()
  }

  const clear = () => {
    handleClear()
  }

  const validate = async (): Promise<boolean> => {
    return (await fieldContainerRef.value?.validate?.()) || false
  }

  const resetField = () => {
    fieldContainerRef.value?.resetField?.()
  }

  const clearValidate = () => {
    fieldContainerRef.value?.clearValidate?.()
  }

  // ===== 暴露给外部使用 =====
  defineExpose({
    /** 使输入框获得焦点 */
    focus,
    /** 使输入框失去焦点 */
    blur,
    /** 选中输入框中的文字 */
    select,
    /** 清空输入框 */
    clear,
    /** 验证字段 */
    validate,
    /** 重置字段 */
    resetField,
    /** 清除验证 */
    clearValidate,
    /** 输入框元素引用 */
    get input() {
      return inputRef.value || null
    },
    /** 包装器元素引用 */
    get wrapper() {
      return fieldContainerRef.value?.wrapper || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'InputFieldCore',
  }
</script>
