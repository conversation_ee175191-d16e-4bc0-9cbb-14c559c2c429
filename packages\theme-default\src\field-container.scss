@use './common/var.scss' as *;

// ===== FieldContainer 基础变量 =====
:root {
  --sp-color-white: #ffffff;
}

// ===== FieldContainer 主样式 =====
.sp-field-container {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;

  // ===== 通用滚动条美化 =====
  // 为所有可滚动元素提供统一的滚动条样式
  * {
    scrollbar-width: thin; // Firefox
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid transparent;
      background-clip: padding-box;
      transition: background-color 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.35);
      }

      &:active {
        background: rgba(0, 0, 0, 0.5);
      }
    }

    &::-webkit-scrollbar-corner {
      background: transparent;
    }
  }

  // ===== 包装器 =====
  &__wrapper {
    position: relative;
    display: flex;
    align-items: stretch;
    width: 100%;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    cursor: text;
    min-height: 48px;
    overflow: visible;
    box-sizing: border-box;
  }

  // ===== 浮动标签 =====
  &__label {
    position: absolute;
    left: 12px;
    top: 16px;
    color: $color-text-secondary;
    font-size: 16px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    transform-origin: left top;
    z-index: 3; // 确保标签始终在最上层
    user-select: none;
    cursor: text;
    background: $background-color-base;
    padding: 0 6px; // 增加左右内边距
    border-radius: 4px;
    white-space: nowrap;
    // 增强背景遮盖效果
    // box-shadow: 0 0 0 2px $background-color-base;

    // 必填星号
    &-asterisk {
      color: $color-danger;
      margin-right: 2px;
    }

    // 浮动状态
    &--floating {
      top: 0;
      transform: translateY(-50%) scale(0.85);
      color: $color-text-secondary;
      font-weight: 500;
    }
  }

  // ===== 聚焦状态下的标签颜色 =====
  &--focused {
    .sp-field-container__label {
      color: $color-primary;
    }
  }

  // ===== 输入元素基础样式 =====
  &__input {
    flex: 1;
    width: 100%;
    padding: 22px 12px 2px 12px;
    border: none;
    outline: none;
    background: transparent;
    color: $color-text-primary;
    font-size: 16px;
    font-family: inherit;
    line-height: 1.5;
    transition: none;
    transform: none;
    will-change: auto;
    position: relative;
    z-index: 1; // 基础层级，确保在标签和功能区域之下
    box-sizing: border-box;

    &::placeholder {
      color: $color-text-secondary;
      opacity: 0.8;
    }

    &:disabled {
      color: $color-text-disabled;
      cursor: not-allowed;

      &::placeholder {
        color: $color-text-disabled;
      }
    }

    &:read-only {
      cursor: default;
    }
  }

  // ===== 前缀区域 =====
  &__prefix {
    position: absolute;
    left: 16px;
    top: 16px;
    bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: $color-text-secondary;
    pointer-events: none;
    z-index: 2;

    // 前缀元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 后缀区域 =====
  &__suffix {
    position: absolute;
    top: 16px;
    right: 16px;
    bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: $color-text-secondary;
    pointer-events: none;
    z-index: 2;

    // 后缀元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 功能区域（向后兼容） =====
  &__functions {
    position: absolute;
    top: 16px;
    right: 16px;
    bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-start;
    gap: 8px;
    color: $color-text-secondary;
    pointer-events: none;
    z-index: 2; // 保持在输入内容之上，但在标签之下

    // 功能元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 加载动画 =====
  &__loading-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    overflow: hidden;
    background: transparent;
  }

  &__loading-progress {
    height: 100%;
    background: $color-primary;
    width: 30%;
    animation: sp-field-container-loading 1.5s infinite ease-in-out;
  }

  // ===== 消息区域 =====
  &__message,
  &__helper {
    margin-top: 6px;
    font-size: 12px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  &__message {
    color: $color-danger;

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  &__helper {
    color: $color-text-secondary;

    &--error {
      color: $color-danger;
    }

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  // ===== 尺寸变体 =====
  &--small {
    .sp-field-container__wrapper {
      min-height: 40px;
    }

    .sp-field-container__input {
      padding: 12px 10px;
      font-size: 14px;
    }

    .sp-field-container__label {
      left: 10px;
      top: 12px;
      font-size: 14px;

      &--floating {
        top: 0;
        transform: translateY(-45%) scale(0.8);
      }
    }

    .sp-field-container__prefix {
      left: 12px;
      top: 12px;
      bottom: 12px;
    }

    .sp-field-container__suffix {
      top: 12px;
      right: 12px;
      bottom: 12px;
    }

    .sp-field-container__functions {
      top: 12px;
      right: 12px;
    }

    // 小尺寸填充、下划线、无边框变体的特殊处理
    &.sp-field-container--filled,
    &.sp-field-container--underlined,
    &.sp-field-container--unborder {
      .sp-field-container__input {
        padding-top: 28px; // 小尺寸的顶部内边距
        padding-bottom: 8px;
      }

      .sp-field-container__label {
        top: 8px;

        &--floating {
          top: 6px;
        }
      }
    }
  }

  &--large {
    .sp-field-container__wrapper {
      min-height: 56px;
    }

    .sp-field-container__input {
      padding: 20px 14px;
      font-size: 18px;
    }

    .sp-field-container__label {
      left: 14px;
      top: 20px;
      font-size: 18px;

      &--floating {
        top: 0;
        transform: translateY(-55%) scale(0.9);
      }
    }

    .sp-field-container__prefix {
      left: 20px;
      top: 20px;
      bottom: 20px;
    }

    .sp-field-container__suffix {
      top: 20px;
      right: 20px;
      bottom: 20px;
    }

    .sp-field-container__functions {
      top: 20px;
      right: 20px;
    }

    // 大尺寸填充、下划线、无边框变体的特殊处理
    &.sp-field-container--filled,
    &.sp-field-container--underlined,
    &.sp-field-container--unborder {
      .sp-field-container__input {
        padding-top: 44px; // 大尺寸的顶部内边距
        padding-bottom: 16px;
      }

      .sp-field-container__label {
        top: 16px;

        &--floating {
          top: 12px;
        }
      }
    }
  }

  // ===== 变体样式 =====

  // 默认变体
  &--default {
    .sp-field-container__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 6px;
      background: var(--sp-color-white);
    }

    .sp-field-container__label {
      background: var(--sp-color-white);
      padding: 0 6px; // 增加内边距
      // 增强背景遮盖效果，确保完全覆盖边框
      box-shadow: 0 0 0 3px var(--sp-color-white);

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
        // 浮动状态下需要更强的背景遮盖
        background: var(--sp-color-white);
        box-shadow: 0 0 0 4px var(--sp-color-white);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 下划线变体 =====
  &--underlined {
    .sp-field-container__wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 0;
      background: transparent;
    }

    .sp-field-container__input {
      // padding-top: 36px; // 和填充样式一致的顶部内边距
      // padding-bottom: 12px;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;
      // top: 12px; // 和填充样式一致的初始位置

      &--floating {
        // top: 8px; // 和填充样式一致的浮动位置
        transform: translateY(0) scale(0.85); // 和填充样式一致的变换
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-bottom-color: $color-primary;
        box-shadow: 0 1px 0 0 $color-primary;
        background: transparent; // 激活时不使用背景色
      }
    }
  }

  // ===== 填充变体 =====
  &--filled {
    .sp-field-container__wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 6px 6px 0 0;
      background: $background-color-hover;
    }

    .sp-field-container__input {
      // padding-top: 36px; // 更大的顶部内边距，让输入区域更往下
      // padding-bottom: 12px;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;
      top: 12px;

      &--floating {
        top: 4px;
        transform: translateY(0) scale(0.85);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-bottom-color: $color-primary;
        background: #e4e7ed;
        box-shadow: 0 1px 0 0 $color-primary;
      }
    }
  }

  // ===== 方形变体 =====
  &--square {
    .sp-field-container__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 0;
      background: var(--sp-color-white);
    }

    .sp-field-container__label {
      background: var(--sp-color-white);
      padding: 0 6px; // 增加内边距
      // 增强背景遮盖效果
      box-shadow: 0 0 0 3px var(--sp-color-white);

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
        // 浮动状态下需要更强的背景遮盖
        background: var(--sp-color-white);
        box-shadow: 0 0 0 4px var(--sp-color-white);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 无边框变体 =====
  &--unborder {
    .sp-field-container__wrapper {
      border: none;
      border-radius: 6px;
      background: transparent;
    }

    .sp-field-container__input {
      // padding-top: 36px; // 和填充样式一致的顶部内边距
      // padding-bottom: 12px;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;
      // top: 12px; // 和填充样式一致的初始位置

      &--floating {
        // top: 8px; // 和填充样式一致的浮动位置
        transform: translateY(0) scale(0.85); // 和填充样式一致的变换
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        background: transparent; // 激活时不使用背景色
      }
    }

    &:hover {
      .sp-field-container__wrapper {
        background: rgba($background-color-hover, 0.5);
      }
    }
  }

  // ===== 状态样式 =====
  &--error {
    .sp-field-container__wrapper {
      border-color: $color-danger !important;
    }

    .sp-field-container__label {
      color: $color-danger !important;
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2) !important;
      }
    }
  }

  &--disabled {
    .sp-field-container__wrapper {
      background: $background-color-disabled;
      border-color: $border-color-disabled;
      cursor: not-allowed;
    }

    .sp-field-container__label {
      color: $color-text-disabled;
    }
  }

  // ===== 发光效果 =====
  &--effect-glow {
    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2),
          0 4px 12px rgba($color-primary, 0.15);
      }
    }

    &.sp-field-container--error.sp-field-container--focused {
      .sp-field-container__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2),
          0 4px 12px rgba($color-danger, 0.15);
      }
    }
  }

  // ===== 有前缀时的输入框样式 =====
  &--has-prefix {
    .sp-field-container__input {
      padding-left: 48px; // 为前缀留空间（默认尺寸）
    }

    &.sp-field-container--small {
      .sp-field-container__input {
        padding-left: 40px; // 小尺寸
      }
    }

    &.sp-field-container--large {
      .sp-field-container__input {
        padding-left: 56px; // 大尺寸
      }
    }
  }

  // ===== 有后缀时的输入框样式 =====
  &--has-suffix {
    .sp-field-container__input {
      padding-right: 48px; // 为后缀留空间（默认尺寸）
    }

    &.sp-field-container--small {
      .sp-field-container__input {
        padding-right: 40px; // 小尺寸
      }
    }

    &.sp-field-container--large {
      .sp-field-container__input {
        padding-right: 56px; // 大尺寸
      }
    }
  }
}

// ===== Textarea 特有样式 =====
.sp-textarea-field {
  &__textarea {
    // 继承 field-container 的输入样式，添加 textarea 特有属性
    min-height: inherit;
    resize: vertical; // 默认垂直调整大小
    font-family: inherit;
    line-height: 1.5;

    // ===== 美化滚动条样式 =====
    scrollbar-width: thin; // Firefox 滚动条宽度
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent; // Firefox 滚动条颜色

    // WebKit 滚动条样式 (Chrome, Safari, Edge)
    &::-webkit-scrollbar {
      width: 8px; // 垂直滚动条宽度
    }

    &::-webkit-scrollbar-track {
      background: transparent; // 滚动条轨道背景
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2); // 滚动条拇指颜色
      border-radius: 4px;
      border: 1px solid transparent;
      background-clip: padding-box;
      transition: background-color 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.35); // 悬停时的颜色
      }

      &:active {
        background: rgba(0, 0, 0, 0.5); // 按下时的颜色
      }
    }

    // 滚动条角落
    &::-webkit-scrollbar-corner {
      background: transparent;
    }

    // 在有浮动标签的变体中，限制内容显示区域
    .sp-field-container--filled &,
    .sp-field-container--underlined &,
    .sp-field-container--unborder & {
      // 使用 mask 来限制内容显示区域，但不影响滚动条
      mask: linear-gradient(
        to bottom,
        transparent 0px,
        transparent 28px,
        black 28px,
        black 100%
      );
      -webkit-mask: linear-gradient(
        to bottom,
        transparent 0px,
        transparent 28px,
        black 28px,
        black 100%
      );

      // 或者使用 padding + negative margin 的方法
      // padding-top: 28px;
      // margin-top: -28px;
      // box-sizing: border-box;
    }

    // 聚焦状态下的滚动条增强效果
    &:focus {
      &::-webkit-scrollbar-thumb {
        background: rgba($color-primary, 0.4);

        &:hover {
          background: rgba($color-primary, 0.6);
        }
      }
    }

    // 错误状态下的滚动条
    .sp-field-container--error & {
      &::-webkit-scrollbar-thumb {
        background: rgba($color-danger, 0.3);

        &:hover {
          background: rgba($color-danger, 0.5);
        }
      }
    }

    // 小尺寸滚动条
    .sp-field-container--small & {
      &::-webkit-scrollbar {
        width: 6px;
      }
    }

    // 大尺寸滚动条
    .sp-field-container--large & {
      &::-webkit-scrollbar {
        width: 10px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 5px;
      }
    }
  }

  &__clear {
    color: $color-text-secondary;
    cursor: pointer;
    transition: color 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    padding: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      color: $color-primary;
      background: rgba(255, 255, 255, 1);
      border-color: $color-primary;
    }
  }

  &__count {
    font-size: 11px;
    color: $color-text-secondary;
    white-space: nowrap;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 2px 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 4px;
  }
}

// ===== InputField 特有样式 =====
.sp-input-field {
  &__input {
    // 继承 field-container 的输入样式
  }

  &__prefix-icon,
  &__suffix-icon {
    color: $color-text-secondary;
    transition: color 0.3s ease;

    &:hover {
      color: $color-primary;
    }
  }

  &__clear,
  &__password {
    color: $color-text-secondary;
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: $color-primary;
    }
  }

  &__count {
    font-size: 11px;
    color: $color-text-secondary;
    white-space: nowrap;
  }
}

// ===== 动画 =====
@keyframes sp-field-container-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(350%);
  }
}

// ===== 消息过渡动画 =====
.sp-field-container-message-enter-active,
.sp-field-container-message-leave-active {
  transition: all 0.3s ease;
}

.sp-field-container-message-enter-from,
.sp-field-container-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// ===== 主题适配滚动条样式 =====
// 深色主题滚动条
[data-theme='dark'] .sp-field-container,
.sp-field-container--dark-theme {
  * {
    scrollbar-color: rgba(255, 255, 255, 0.4) transparent;

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }

      &:active {
        background: rgba(255, 255, 255, 0.7);
      }
    }
  }

  .sp-textarea-field__textarea {
    scrollbar-color: rgba(255, 255, 255, 0.4) transparent;

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    &:focus {
      &::-webkit-scrollbar-thumb {
        background: rgba($color-primary, 0.6);

        &:hover {
          background: rgba($color-primary, 0.8);
        }
      }
    }
  }
}

// 高对比度主题滚动条
[data-theme='high-contrast'] .sp-field-container,
.sp-field-container--high-contrast {
  * {
    scrollbar-color: #000000 #ffffff;

    &::-webkit-scrollbar-thumb {
      background: #000000;
      border: 1px solid #ffffff;

      &:hover {
        background: #333333;
      }
    }

    &::-webkit-scrollbar-track {
      background: #ffffff;
      border: 1px solid #000000;
    }
  }
}

// 禁用状态滚动条样式
.sp-field-container--disabled {
  * {
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      cursor: not-allowed;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .sp-textarea-field__textarea {
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      cursor: not-allowed;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}
