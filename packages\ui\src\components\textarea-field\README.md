# TextareaField 组件

TextareaField 是一个带有浮动标签和表单验证功能的文本域组件，基于 Textarea 组件扩展而来。

## 特性

- ✨ **浮动标签** - 支持浮动动画标签效果
- 🔍 **表单验证** - 集成 VeeValidate 表单验证
- 📏 **字数统计** - 支持字数限制和统计显示
- 🧹 **可清除** - 支持一键清除内容
- 📐 **自动调整** - 支持自动调整高度
- 🎨 **多种尺寸** - 支持 small、medium、large 三种尺寸
- 🔧 **调整大小** - 支持多种调整大小选项
- ♿ **无障碍** - 完整的无障碍支持

## 基础用法

```vue
<template>
  <sp-textarea-field
    v-model:value="value"
    label="描述"
    name="description"
    placeholder="请输入描述..."
    :rows="3"
  />
</template>

<script setup>
  import { ref } from 'vue'

  const value = ref('')
</script>
```

## 必填字段

```vue
<template>
  <sp-textarea-field
    v-model:value="value"
    label="必填字段"
    name="required"
    :required="true"
    placeholder="这是必填字段..."
  />
</template>
```

## 字数统计

```vue
<template>
  <sp-textarea-field
    v-model:value="value"
    label="限制字数"
    name="limited"
    :maxlength="100"
    :show-word-limit="true"
    placeholder="最多100字..."
  />
</template>
```

## 自动调整高度

```vue
<template>
  <sp-textarea-field
    v-model:value="value"
    label="自动高度"
    name="autosize"
    :autosize="{ minRows: 2, maxRows: 6 }"
    placeholder="高度会自动调整..."
  />
</template>
```

## 表单验证

```vue
<template>
  <sp-textarea-field
    v-model:value="value"
    label="验证字段"
    name="validated"
    :rules="validationRules"
    placeholder="输入内容进行验证..."
  />
</template>

<script setup>
  import { ref } from 'vue'

  const value = ref('')

  const validationRules = value => {
    if (!value) return '此字段为必填项'
    if (value.length < 10) return '至少需要10个字符'
    return true
  }
</script>
```

## API

### Props

| 属性              | 类型                                                              | 默认值       | 说明                       |
| ----------------- | ----------------------------------------------------------------- | ------------ | -------------------------- |
| `value`           | `string`                                                          | `''`         | 绑定值，支持 v-model:value |
| `label`           | `string`                                                          | -            | 浮动标签文本               |
| `name`            | `string`                                                          | -            | 表单字段名，用于验证       |
| `placeholder`     | `string`                                                          | -            | 占位符文本                 |
| `rows`            | `number`                                                          | `3`          | 文本域行数                 |
| `cols`            | `number`                                                          | -            | 文本域列数                 |
| `resize`          | `'none' \| 'both' \| 'horizontal' \| 'vertical'`                  | `'vertical'` | 调整大小方式               |
| `autosize`        | `boolean \| { minRows?: number; maxRows?: number }`               | `false`      | 是否自动调整高度           |
| `maxlength`       | `number`                                                          | -            | 最大字符数                 |
| `showWordLimit`   | `boolean`                                                         | `false`      | 是否显示字数统计           |
| `clearable`       | `boolean`                                                         | `false`      | 是否可清除                 |
| `disabled`        | `boolean`                                                         | `false`      | 是否禁用                   |
| `readonly`        | `boolean`                                                         | `false`      | 是否只读                   |
| `required`        | `boolean`                                                         | `false`      | 是否必填                   |
| `error`           | `boolean`                                                         | `false`      | 是否错误状态               |
| `size`            | `'small' \| 'medium' \| 'large'`                                  | `'medium'`   | 组件尺寸                   |
| `variant`         | `'default' \| 'underlined' \| 'filled' \| 'square' \| 'unborder'` | `'default'`  | 外观变体                   |
| `effect`          | `'none' \| 'glow'`                                                | `'none'`     | 视觉效果                   |
| `rules`           | `any`                                                             | -            | 验证规则                   |
| `showMessage`     | `boolean`                                                         | `true`       | 是否显示验证消息           |
| `helperText`      | `string`                                                          | -            | 帮助文本                   |
| `persistentLabel` | `boolean`                                                         | `false`      | 标签是否始终浮动           |

### Events

| 事件名         | 参数                                                | 说明         |
| -------------- | --------------------------------------------------- | ------------ |
| `update:value` | `(value: string \| undefined)`                      | 值更新事件   |
| `change`       | `(value: string \| undefined)`                      | 值改变事件   |
| `input`        | `(event: Event)`                                    | 输入事件     |
| `focus`        | `(event: FocusEvent)`                               | 获得焦点事件 |
| `blur`         | `(event: FocusEvent)`                               | 失去焦点事件 |
| `clear`        | `()`                                                | 清除事件     |
| `validate`     | `(name: string, isValid: boolean, message: string)` | 验证事件     |

### Methods

| 方法名          | 参数 | 返回值             | 说明                              |
| --------------- | ---- | ------------------ | --------------------------------- |
| `focus`         | -    | `void`             | 使文本域获得焦点                  |
| `blur`          | -    | `void`             | 使文本域失去焦点                  |
| `select`        | -    | `void`             | 选中文本域中的文字                |
| `clear`         | -    | `void`             | 清空文本域                        |
| `validate`      | -    | `Promise<boolean>` | 验证字段                          |
| `resetField`    | -    | `void`             | 重置字段                          |
| `clearValidate` | -    | `void`             | 清除验证                          |
| `adjustHeight`  | -    | `void`             | 调整文本域高度（autosize 模式下） |

### Slots

| 插槽名   | 说明     |
| -------- | -------- |
| `prefix` | 前缀内容 |
| `suffix` | 后缀内容 |

## 样式定制

TextareaField 组件使用 CSS 变量进行样式定制，你可以通过覆盖这些变量来自定义外观：

```css
.sp-textarea-field {
  --sp-color-primary: #your-color;
  --sp-color-danger: #your-error-color;
  --sp-border-radius: 8px;
}
```

## 与 InputField 的区别

TextareaField 基于 InputField 的设计模式，但专门为多行文本输入优化：

- 使用 `<textarea>` 元素而不是 `<input>`
- 支持多行文本输入
- 支持自动调整高度
- 支持不同的调整大小选项
- 针对多行文本的样式优化
