/**
 * FieldContainer 通用表单字段容器类型定义
 */

/** FieldContainer 基础属性 */
export interface FieldContainerProps {
  /** 绑定值 */
  value?: string | number
  /** 标签文本 */
  label?: string
  /** 表单字段名 */
  name?: string
  /** 占位符文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否必填 */
  required?: boolean
  /** 是否错误状态 */
  error?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 外观变体 */
  variant?: 'default' | 'underlined' | 'filled' | 'square' | 'unborder'
  /** 视觉效果 */
  effect?: 'none' | 'glow'
  /** 组件尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 验证规则 */
  rules?: any
  /** 是否显示验证消息 */
  showMessage?: boolean
  /** 帮助文本 */
  helperText?: string
  /** 标签是否始终浮动 */
  persistentLabel?: boolean
}

/** FieldContainer 事件 */
export interface FieldContainerEmits {
  /** 值更新事件 */
  (e: 'update:value', value: string | number | undefined): void
  /** 输入事件 */
  (e: 'input', event: Event): void
  /** 值改变事件 */
  (e: 'change', event: Event): void
  /** 获得焦点事件 */
  (e: 'focus', event: FocusEvent): void
  /** 失去焦点事件 */
  (e: 'blur', event: FocusEvent): void
  /** 键盘事件 */
  (e: 'keydown', event: KeyboardEvent): void
  /** 包装器点击事件 */
  (e: 'wrapper-click'): void
  /** 标签点击事件 */
  (e: 'label-click'): void
  /** 验证事件 */
  (e: 'validate', name: string, isValid: boolean, message: string): void
}

/** FieldContainer 实例方法 */
export interface FieldContainerInstance {
  /** 验证字段 */
  validate: () => Promise<boolean>
  /** 重置字段 */
  resetField: () => void
  /** 清除验证 */
  clearValidate: () => void
  /** 包装器元素引用 */
  wrapper: HTMLDivElement | null
  /** 是否聚焦状态 */
  isFocused: boolean
  /** 是否有值 */
  hasValue: boolean
  /** 标签是否浮动 */
  isLabelFloating: boolean
  /** 验证状态 */
  validateState: string
}

/** 插槽 Props 类型 */
export interface FieldContainerSlotProps {
  /** 字段 ID */
  fieldId: string
  /** 输入元素类名 */
  inputClasses: string[]
  /** 输入元素样式 */
  inputStyle: Record<string, any>
  /** 占位符文本 */
  placeholder: string
  /** 是否禁用 */
  disabled: boolean
  /** 是否只读 */
  readonly: boolean
  /** 聚焦事件处理 */
  onFocus: (event: FocusEvent) => void
  /** 失焦事件处理 */
  onBlur: (event: FocusEvent) => void
  /** 输入事件处理 */
  onInput: (event: Event) => void
  /** 改变事件处理 */
  onChange: (event: Event) => void
  /** 键盘事件处理 */
  onKeydown: (event: KeyboardEvent) => void
}

/** 功能区域插槽 Props 类型 */
export interface FieldContainerFunctionsSlotProps {
  /** 功能区域类名 */
  functionsClasses: string[]
  /** 图标尺寸 */
  iconSize: number
}
