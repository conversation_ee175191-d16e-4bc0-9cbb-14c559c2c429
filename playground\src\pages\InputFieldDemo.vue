<template>
  <div class="page-container">
    <h1>InputField 组件演示</h1>
    <p>
      带有浮动标签的输入框组件，可直接作为表单字段使用，集成了所有 Input
      组件功能
    </p>

    <div class="demo-section">
      <h2>🌟 浮动标签基础用法</h2>
      <div class="demo-item">
        <label>基础浮动标签：</label>
        <sp-input-field
          v-model:value="basicValue"
          name="basic"
          label="用户名"
          placeholder="请输入用户名"
          @change="handleChange"
        />
        <span class="value-display">当前值：{{ basicValue || '(空)' }}</span>
      </div>
      <div class="demo-item">
        <label>必填字段：</label>
        <sp-input-field
          v-model:value="requiredValue"
          name="required"
          label="邮箱地址"
          placeholder="请输入邮箱"
          required
          :rules="emailRules"
        />
        <span class="value-display">标签前有红色星号，支持验证</span>
      </div>
      <div class="demo-item">
        <label>始终浮动：</label>
        <sp-input-field
          v-model:value="persistentValue"
          name="persistent"
          label="项目名称"
          placeholder="请输入项目名称"
          persistent-label
        />
        <span class="value-display">标签始终保持在上方</span>
      </div>
      <div class="demo-item">
        <label>带帮助文本：</label>
        <sp-input-field
          v-model:value="helperValue"
          name="helper"
          label="密码"
          type="password"
          placeholder="请输入密码"
          helper-text="密码长度至少8位，包含字母和数字"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🎨 变体样式 + 浮动标签</h2>
      <div class="demo-item">
        <label>默认样式：</label>
        <sp-input-field
          v-model:value="defaultVariantValue"
          name="default"
          label="默认边框"
          variant="default"
          prefix-icon="Mail"
          placeholder="默认边框样式"
        />
      </div>
      <div class="demo-item">
        <label>下划线样式：</label>
        <sp-input-field
          v-model:value="underlinedVariantValue"
          name="underlined"
          label="下划线输入框"
          variant="underlined"
          prefix-icon="Mail"
          placeholder="简洁的下划线样式"
        />
        <span class="value-display">点击查看标签上浮和下划线扩展动画</span>
      </div>
      <div class="demo-item">
        <label>填充样式：</label>
        <sp-input-field
          v-model:value="filledVariantValue"
          name="filled"
          label="填充输入框"
          variant="filled"
          placeholder="带背景的填充样式"
        />
        <span class="value-display">背景色填充，标签浮动到内部顶部</span>
      </div>

      <div class="demo-item">
        <label>方形样式：</label>
        <sp-input-field
          v-model:value="squareVariantValue"
          name="square"
          label="方形输入框"
          variant="square"
          placeholder="方正的边框样式"
        />
        <span class="value-display">无圆角，方正设计</span>
      </div>
      <div class="demo-item">
        <label>无边框样式：</label>
        <sp-input-field
          v-model:value="unborderVariantValue"
          name="unborder"
          label="无边框输入框"
          variant="unborder"
          placeholder="极简无边框设计"
        />
        <span class="value-display">完全无边框，hover 显示背景</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>✨ 发光效果 + 浮动标签</h2>
      <div class="demo-item">
        <label>默认发光：</label>
        <sp-input-field
          v-model:value="glowDefaultValue"
          name="glow-default"
          label="发光输入框"
          variant="default"
          effect="glow"
          placeholder="聚焦时有发光效果"
        />
      </div>
      <div class="demo-item">
        <label>下划线发光：</label>
        <sp-input-field
          v-model:value="glowUnderlineValue"
          name="glow-underline"
          label="发光下划线"
          variant="underlined"
          effect="glow"
          placeholder="下划线 + 发光效果"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>📏 不同尺寸</h2>
      <div class="demo-item">
        <label>小尺寸：</label>
        <sp-input-field
          v-model:value="smallSizeValue"
          name="small"
          label="小尺寸输入框"
          size="small"
          placeholder="紧凑的小尺寸"
        />
      </div>
      <div class="demo-item">
        <label>中等尺寸：</label>
        <sp-input-field
          v-model:value="mediumSizeValue"
          name="medium"
          label="中等尺寸输入框"
          size="medium"
          placeholder="标准的中等尺寸"
        />
      </div>
      <div class="demo-item">
        <label>大尺寸：</label>
        <sp-input-field
          v-model:value="largeSizeValue"
          name="large"
          label="大尺寸输入框"
          size="large"
          placeholder="舒适的大尺寸"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🔧 功能组合</h2>
      <div class="demo-item">
        <label>密码输入：</label>
        <sp-input-field
          v-model:value="passwordValue"
          name="password"
          label="登录密码"
          type="password"
          show-password
          placeholder="请输入密码"
        />
      </div>
      <div class="demo-item">
        <label>可清除：</label>
        <sp-input-field
          v-model:value="clearableValue"
          name="clearable"
          label="搜索关键词"
          clearable
          placeholder="输入搜索内容"
        />
      </div>
      <div class="demo-item">
        <label>字数限制：</label>
        <sp-input-field
          v-model:value="limitValue"
          name="limit"
          label="个人简介"
          :maxlength="50"
          show-word-limit
          placeholder="最多50个字符"
        />
      </div>
      <div class="demo-item">
        <label>前缀图标：</label>
        <sp-input-field
          v-model:value="prefixIconValue"
          name="prefix"
          label="邮箱地址"
          prefix-icon="Mail"
          placeholder="<EMAIL>"
        />
      </div>
      <div class="demo-item">
        <label>后缀图标：</label>
        <sp-input-field
          v-model:value="suffixIconValue"
          name="suffix"
          label="用户设置"
          suffix-icon="Settings"
          placeholder="配置用户信息"
        />
      </div>
      <div class="demo-item">
        <label>完整组合：</label>
        <sp-input-field
          v-model:value="fullFeatureValue"
          name="full"
          label="完整功能演示"
          variant="filled"
          effect="glow"
          size="large"
          prefix-icon="Person"
          clearable
          :maxlength="30"
          show-word-limit
          placeholder="所有功能组合使用"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🔄 加载状态</h2>
      <div class="demo-item">
        <label>加载中：</label>
        <sp-input-field
          v-model:value="loadingValue"
          name="loading"
          label="数据加载中"
          loading
          placeholder="加载状态的输入框"
        />
        <span class="value-display">loading 状态下自动禁用</span>
      </div>
      <div class="demo-item">
        <label>动态控制：</label>
        <sp-input-field
          v-model:value="toggleLoadingValue"
          name="toggle-loading"
          label="可控制加载"
          :loading="isToggleLoading"
          placeholder="可以控制的加载状态"
        />
        <div class="button-group">
          <button @click="toggleLoading">
            {{ isToggleLoading ? '停止加载' : '开始加载' }}
          </button>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>⚠️ 状态演示</h2>
      <div class="demo-item">
        <label>正常状态：</label>
        <sp-input-field
          v-model:value="normalStateValue"
          name="normal"
          label="正常输入框"
          placeholder="正常状态"
        />
      </div>
      <div class="demo-item">
        <label>禁用状态：</label>
        <sp-input-field
          v-model:value="disabledStateValue"
          name="disabled"
          label="禁用输入框"
          disabled
          placeholder="禁用状态"
        />
      </div>
      <div class="demo-item">
        <label>只读状态：</label>
        <sp-input-field
          v-model:value="readonlyStateValue"
          name="readonly"
          label="只读输入框"
          readonly
          placeholder="只读状态"
        />
      </div>
      <div class="demo-item">
        <label>错误状态：</label>
        <sp-input-field
          v-model:value="errorStateValue"
          name="error"
          label="错误输入框"
          error
          placeholder="错误状态"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🧪 表单验证演示</h2>
      <div class="demo-item">
        <label>邮箱验证：</label>
        <sp-input-field
          v-model:value="emailValidateValue"
          name="email-validate"
          label="邮箱地址"
          :rules="emailValidationRules"
          placeholder="输入邮箱进行验证"
        />
        <span class="value-display">失去焦点时自动验证</span>
      </div>
      <div class="demo-item">
        <label>密码强度：</label>
        <sp-input-field
          v-model:value="passwordValidateValue"
          name="password-validate"
          label="设置密码"
          type="password"
          :rules="passwordValidationRules"
          show-password
          placeholder="至少8位，包含数字和字母"
        />
      </div>
      <div class="demo-item">
        <label>手机号码：</label>
        <sp-input-field
          v-model:value="phoneValidateValue"
          name="phone-validate"
          label="手机号码"
          :rules="phoneValidationRules"
          placeholder="请输入11位手机号"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>🎯 方法调用</h2>
      <div class="demo-item">
        <label>方法测试：</label>
        <sp-input-field
          ref="methodFieldRef"
          v-model:value="methodValue"
          name="method"
          label="方法测试"
          placeholder="测试组件方法"
        />
        <div class="button-group">
          <button @click="focusField">聚焦</button>
          <button @click="blurField">失焦</button>
          <button @click="selectField">选中</button>
          <button @click="clearField">清空</button>
          <button @click="validateField">验证</button>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>📝 插槽演示</h2>
      <div class="demo-item">
        <label>前缀插槽：</label>
        <sp-input-field
          v-model:value="prefixSlotValue"
          name="prefix-slot"
          label="前缀插槽"
          placeholder="自定义前缀内容"
        >
          <template #prefix>
            <sp-icon
              name="Search"
              :size="16"
              color="#409eff"
            />
          </template>
        </sp-input-field>
      </div>
      <div class="demo-item">
        <label>后缀插槽：</label>
        <sp-input-field
          v-model:value="suffixSlotValue"
          name="suffix-slot"
          label="后缀插槽"
          placeholder="自定义后缀内容"
        >
          <template #suffix>
            <sp-icon
              name="Send"
              :size="16"
              color="#67c23a"
              clickable
            />
          </template>
        </sp-input-field>
      </div>
    </div>

    <div class="demo-section">
      <h2>📋 API 使用说明</h2>
      <div class="api-info">
        <h3>🌟 新增特性</h3>
        <ul>
          <li>
            <code>label</code>
            - 浮动标签文本，点击时自动聚焦到输入框
          </li>
          <li>
            <code>name</code>
            - 表单字段名称，用于表单验证和标识
          </li>
          <li>
            <code>required</code>
            - 是否必填，显示红色星号
          </li>
          <li>
            <code>rules</code>
            - 验证规则，支持 VeeValidate 规则
          </li>
          <li>
            <code>helper-text</code>
            - 帮助文本，在无验证错误时显示
          </li>
          <li>
            <code>persistent-label</code>
            - 标签是否始终浮动
          </li>
          <li>
            <code>show-message</code>
            - 是否显示验证消息
          </li>
        </ul>

        <h3>🎯 浮动标签行为</h3>
        <ul>
          <li>默认标签位于输入框内部</li>
          <li>聚焦、有值或有 placeholder 时自动上浮</li>
          <li>上浮时标签缩小并改变颜色</li>
          <li>点击标签会自动聚焦到输入框</li>
          <li>支持所有变体样式的适配</li>
        </ul>

        <h3>📋 表单集成</h3>
        <ul>
          <li>
            可直接作为
            <code>sp-form</code>
            的子组件使用
          </li>
          <li>
            无需包装
            <code>sp-form-item</code>
          </li>
          <li>自动集成 VeeValidate 验证</li>
          <li>支持实时验证和失焦验证</li>
          <li>错误消息自动显示和隐藏</li>
        </ul>

        <h3>🔄 继承功能</h3>
        <ul>
          <li>
            完全继承
            <code>sp-input</code>
            的所有功能
          </li>
          <li>
            支持所有变体：default, underlined, filled, ghost, pill, square,
            unborder
          </li>
          <li>支持所有尺寸：small, medium, large</li>
          <li>支持发光效果：effect="glow"</li>
          <li>支持所有功能：密码切换、清除、字数统计、图标、加载等</li>
        </ul>

        <h3>⚡ 方法调用</h3>
        <ul>
          <li>
            <code>focus()</code>
            - 聚焦输入框
          </li>
          <li>
            <code>blur()</code>
            - 失焦输入框
          </li>
          <li>
            <code>select()</code>
            - 选中文本
          </li>
          <li>
            <code>clear()</code>
            - 清空内容
          </li>
          <li>
            <code>validate()</code>
            - 手动验证
          </li>
          <li>
            <code>resetField()</code>
            - 重置字段
          </li>
          <li>
            <code>clearValidate()</code>
            - 清除验证状态
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 基础功能
  const basicValue = ref('')
  const requiredValue = ref('')
  const persistentValue = ref('')
  const helperValue = ref('')

  // 变体样式
  const defaultVariantValue = ref('')
  const underlinedVariantValue = ref('')
  const filledVariantValue = ref('')
  const squareVariantValue = ref('')
  const unborderVariantValue = ref('')

  // 发光效果
  const glowDefaultValue = ref('')
  const glowUnderlineValue = ref('')

  // 尺寸
  const smallSizeValue = ref('')
  const mediumSizeValue = ref('')
  const largeSizeValue = ref('')

  // 功能组合
  const passwordValue = ref('')
  const clearableValue = ref('可以清除这段文字')
  const limitValue = ref('')
  const prefixIconValue = ref('')
  const suffixIconValue = ref('')
  const fullFeatureValue = ref('')

  // 加载状态
  const loadingValue = ref('')
  const toggleLoadingValue = ref('')
  const isToggleLoading = ref(false)

  // 状态演示
  const normalStateValue = ref('正常状态示例')
  const disabledStateValue = ref('禁用状态示例')
  const readonlyStateValue = ref('只读状态示例')
  const errorStateValue = ref('')

  // 验证演示
  const emailValidateValue = ref('')
  const passwordValidateValue = ref('')
  const phoneValidateValue = ref('')

  // 方法测试
  const methodValue = ref('测试方法调用')

  // 插槽演示
  const prefixSlotValue = ref('')
  const suffixSlotValue = ref('')

  // 组件引用
  const methodFieldRef = ref()

  // 验证规则
  const emailRules = 'required|email'
  const emailValidationRules = 'required|email'
  const passwordValidationRules = 'required|min:8'
  const phoneValidationRules = 'required|regex:^1[3-9]\\d{9}$'

  // 事件处理
  const handleChange = (value: string | number | undefined) => {
    console.log('InputField change:', value)
  }

  // 加载控制
  const toggleLoading = () => {
    isToggleLoading.value = !isToggleLoading.value
  }

  // 方法调用
  const focusField = () => {
    methodFieldRef.value?.focus()
  }

  const blurField = () => {
    methodFieldRef.value?.blur()
  }

  const selectField = () => {
    methodFieldRef.value?.select()
  }

  const clearField = () => {
    methodFieldRef.value?.clear()
  }

  const validateField = async () => {
    const isValid = await methodFieldRef.value?.validate()
    console.log('验证结果:', isValid)
  }
</script>

<style scoped>
  .page-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .page-container h1 {
    color: #303133;
    font-size: 28px;
    margin-bottom: 10px;
  }

  .page-container > p {
    color: #606266;
    font-size: 14px;
    margin-bottom: 30px;
  }

  .demo-section {
    margin-bottom: 40px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    overflow: hidden;
  }

  .demo-section h2 {
    margin: 0;
    padding: 15px 20px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    color: #303133;
    font-size: 18px;
    font-weight: 600;
  }

  .demo-item {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: flex-start;
    gap: 15px;
  }

  .demo-item:last-child {
    border-bottom: none;
  }

  .demo-item label {
    width: 120px;
    flex-shrink: 0;
    font-weight: 500;
    color: #606266;
    font-size: 14px;
    line-height: 2.5;
  }

  .demo-item .sp-input-field {
    min-width: 280px;
  }

  .value-display {
    color: #909399;
    font-size: 12px;
    line-height: 2.5;
    margin-left: 10px;
  }

  .button-group {
    display: flex;
    gap: 8px;
    margin-left: 10px;
    align-items: center;
  }

  .button-group button {
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #fff;
    cursor: pointer;
    font-size: 12px;
    color: #606266;
    transition: all 0.2s;
  }

  .button-group button:hover {
    border-color: #409eff;
    color: #409eff;
  }

  .api-info {
    padding: 20px;
  }

  .api-info h3 {
    margin: 20px 0 10px 0;
    color: #303133;
    font-size: 16px;
  }

  .api-info h3:first-child {
    margin-top: 0;
  }

  .api-info code {
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #e74c3c;
  }

  .api-info ul {
    margin: 0;
    padding-left: 20px;
  }

  .api-info li {
    margin: 5px 0;
    font-size: 14px;
    color: #606266;
  }
</style>
