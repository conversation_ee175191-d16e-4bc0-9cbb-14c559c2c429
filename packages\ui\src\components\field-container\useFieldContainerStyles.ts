import { computed, type Ref } from 'vue'
import { useBEM } from '@speed-ui/bem-helper'
import type { FieldContainerProps } from './types'

interface FieldContainerLogicState {
  computedDisabled: Ref<boolean>
  isFocused: Ref<boolean>
  hasValue: Ref<boolean>
  isLabelFloating: Ref<boolean>
  validateState: Ref<string>
}

export function useFieldContainerStyles(
  props: FieldContainerProps,
  logicState: FieldContainerLogicState
) {
  const bem = useBEM('field-container')

  // ===== 根容器类名 =====
  const rootClasses = computed(() => [
    bem.b(),
    bem.m(props.size || 'medium'),
    bem.m(props.variant || 'default'),
    bem.m(`effect-${props.effect || 'none'}`),
    {
      [bem.m('disabled')]: logicState.computedDisabled.value,
      [bem.m('readonly')]: props.readonly,
      [bem.m('focused')]: logicState.isFocused.value,
      [bem.m('error')]:
        props.error || logicState.validateState.value === 'error',
      [bem.m('warning')]: logicState.validateState.value === 'warning',
      [bem.m('success')]: logicState.validateState.value === 'success',
      [bem.m('loading')]: props.loading,
      [bem.m('has-value')]: logicState.hasValue.value,
      [bem.m('label-floating')]: logicState.isLabelFloating.value,
      [bem.m('persistent-label')]: props.persistentLabel,
    },
  ])

  // ===== 各部分类名 =====
  const wrapperClasses = computed(() => [bem.e('wrapper')])

  const labelClasses = computed(() => [
    bem.e('label'),
    {
      [bem.em('label', 'floating')]: logicState.isLabelFloating.value,
      [bem.em('label', 'required')]: props.required,
    },
  ])

  const inputClasses = computed(() => [bem.e('input')])

  const inputStyle = computed(() => ({}))

  const prefixClasses = computed(() => [bem.e('prefix')])

  const suffixClasses = computed(() => [bem.e('suffix')])

  const functionsClasses = computed(() => [bem.e('functions')])

  const loadingBarClasses = computed(() => [bem.e('loading-bar')])

  const loadingProgressClasses = computed(() => [bem.e('loading-progress')])

  const helperTextClasses = computed(() => [
    bem.e('helper'),
    {
      [bem.em('helper', 'error')]:
        props.error || logicState.validateState.value === 'error',
      [bem.em('helper', 'warning')]:
        logicState.validateState.value === 'warning',
      [bem.em('helper', 'success')]:
        logicState.validateState.value === 'success',
    },
  ])

  const messageClasses = computed(() => [
    bem.e('message'),
    bem.em('message', logicState.validateState.value || 'error'),
  ])

  return {
    // 类名
    rootClasses,
    wrapperClasses,
    labelClasses,
    inputClasses,
    inputStyle,
    prefixClasses,
    suffixClasses,
    functionsClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  }
}
